package com.blackj.springbootdemo.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class HormoneDTO {
    private String test_time;

    private TestResult test_results;

    @Getter
    @Setter
    public static class TestResult {
        private Integer wand_type;

        private Float value1;

        private Float value2;

        private Float value3;

        @JsonProperty("Ecode")
        private String Ecode;
    }

    private String wandBatch3;

    private Long id;

    private Integer flag = 1;

    private ExtraInfo extra_info;

    @Getter
    @Setter
    public static class ExtraInfo {
        private Float hcg_limitupper2;
    }
}
