package com.blackj.springbootdemo.algorithm;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Description LRU 最近最少使用算法
 */
public class LruCache<K, V> extends LinkedHashMap<K, V> {
    /**
     * 容量
     */
    private int capacity;

    /**
     * 存储的缓存容量
     *
     * @param capacity 缓存大小
     */
    public LruCache(int capacity) {
        /*
        accessOrder 为 ture 时，每访问一个元素，就将该元素置于链表尾部，
        所以 accessOrder 就是起到控制访问顺序的作用。
         */
        super(capacity, 0.75f, true);
        this.capacity = capacity;
    }

    /**
     * 如果数据大于设定的容量时，返回 true 时将删除最老的数据，也就是 HEAD 链表头部的元素
     *
     * @param eldest 最老的数据
     * @return 返回 ture 时删除最老的数据
     */
    @Override
    protected boolean removeEldestEntry(Map.Entry<K, V> eldest) {
        return size() > capacity;
    }
}
