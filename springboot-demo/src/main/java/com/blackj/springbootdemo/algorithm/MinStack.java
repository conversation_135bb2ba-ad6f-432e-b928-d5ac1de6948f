package com.blackj.springbootdemo.algorithm;

import java.util.Stack;

/**
 * <AUTHOR>
 * Description getMin（），获取栈内最小值
 */
public class MinStack {
    private Stack<Integer> normalStack = new Stack<>();
    private Stack<Integer> minStack = new Stack<>();

    public void push(Integer value) {
        normalStack.push(value);
        if (minStack.empty() || value <= minStack.peek()) {
            minStack.push(value);
        }
    }

    public Integer pop() {
        Integer value = normalStack.pop();
        if (value.equals(minStack.peek())) {
            minStack.pop();
        }
        return value;
    }

    public boolean empty() {
        return normalStack.empty();
    }

    public Integer getMin() throws Exception {
        if (minStack.empty()) {
            throw new Exception("栈元素为空");
        }
        return minStack.peek();
    }

    public static void main(String[] args) throws Exception {
        MinStack minStack = new MinStack();
        minStack.push(8);
        minStack.push(5);
        minStack.push(6);
        minStack.push(3);
        minStack.push(3);
        minStack.push(6);
        minStack.push(7);
        minStack.push(1);
        minStack.push(9);
        minStack.push(4);

        System.out.println("min:" + minStack.getMin());
        System.out.println("--------");
        while (!minStack.empty()) {
            System.out.println("pop:" + minStack.pop());
            System.out.println("min:" + minStack.getMin());
            System.out.println("--------");
        }
    }
}
