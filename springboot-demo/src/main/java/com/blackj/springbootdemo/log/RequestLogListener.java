package com.blackj.springbootdemo.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.blackj.springbootdemo.spring.RequestLogEvent;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.event.EventListener;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * Description
 */
@Slf4j
@Component
public class RequestLogListener {
    @EventListener(RequestLogEvent.class)
    public void logListener(RequestLogEvent event) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        CompletableFuture.runAsync(() -> {
            System.out.println("当前线程：" + Thread.currentThread().getName());
            // 入参
            requestParamCollection((JoinPoint) event.getSource(), request);
            // 出参
            responseParamCollection(event.getResult());
        });
    }

    /**
     * 入参打印、存储
     */
    private void requestParamCollection(JoinPoint joinPoint, HttpServletRequest request) {
        Signature signature = joinPoint.getSignature();

        log.info("请求URL：{}", request.getRequestURL().toString());
        log.info("方法名：{}", String.format("%s.%s", signature.getDeclaringTypeName(), signature.getName()));

        // 请求参数
        log.info("请求参数：{}", requestParam2Json(signature, joinPoint.getArgs()));
    }

    /**
     * 请求参数，json
     */
    private String requestParam2Json(Signature signature, Object[] args) {
        MethodSignature methodSignature = (MethodSignature) signature;
        Method method = methodSignature.getMethod();
        LocalVariableTableParameterNameDiscoverer parameterNameDiscoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] paramNames = parameterNameDiscoverer.getParameterNames(method);
        Map<String, Object> parameterMap = Maps.newHashMap();
        if (args != null && paramNames != null) {
            for (int i = 0; i < args.length; ++i) {
                Object value = args[i];
                if (value instanceof MultipartFile) {
                    MultipartFile file = (MultipartFile) value;
                    value = file.getOriginalFilename();
                }

                if (!(value instanceof ServletRequest) && !(value instanceof ServletResponse)) {
                    parameterMap.put(paramNames[i], value);
                }
            }
        }

        return JSON.toJSONString(parameterMap, SerializerFeature.WriteMapNullValue);
    }

    /**
     * 出参打印、存储
     */
    private void responseParamCollection(Object result) {
        log.info("返回结果：{}", JSON.toJSONString(result, SerializerFeature.WriteMapNullValue));
    }
}
