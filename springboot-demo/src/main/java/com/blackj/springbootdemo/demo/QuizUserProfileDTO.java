package com.blackj.springbootdemo.demo;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 描述
 * </p>
 *
 * @author: xizhao.dai
 * @since: 2023-07-05
 **/
@Getter
@Setter
public class QuizUserProfileDTO {
    /**
     * Please confirm your birth date:
     */
    private Long birthDateDTO;
    /**
     * Would you say your cycle is regular?
     */
    private Integer cycleRegular;
    private Integer cycleLength;
    private List<Integer> cycleLengthRange;
    private List<Integer> periodLengthRange;
    /**
     * When was your last period start date?
     */
    private Long periodDateDTO;
    /**
     * Do you experience any of the following conditions?
     */
    private List<Integer> conditions;
    /**
     * Have you ever tracked your hormones before?
     */
    private Integer trackHormones;
}
