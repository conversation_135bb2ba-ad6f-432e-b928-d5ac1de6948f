package com.blackj.springbootdemo.demo;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class KlaviyoMetricEventDTO {
    private Data data;

    public KlaviyoMetricEventDTO(String email, String metricName, String subject) {
        Attributes attributes = new Attributes(new Profile(email), new Metric(metricName), new Properties(subject));
        this.data = new Data(attributes);
    }

    @Getter
    @Setter
    private static class Data {
        private String type = "event";
        private Attributes attributes;

        public Data(Attributes attributes) {
            this.attributes = attributes;
        }
    }

    @Getter
    @Setter
    private static class Attributes {
        private Profile profile;
        private Metric metric;
        private Properties properties;

        public Attributes(Profile profile, Metric metric, Properties properties) {
            this.profile = profile;
            this.metric = metric;
            this.properties = properties;
        }
    }

    @Getter
    @Setter
    private static class Profile {
        private ProfileData data = new ProfileData();

        public Profile(String email) {
            data.getAttributes().setEmail(email);
        }
    }

    @Getter
    @Setter
    private static class ProfileData {
        private String type = "profile";
        private ProfileDataAttributes attributes = new ProfileDataAttributes();
    }

    @Getter
    @Setter
    private static class ProfileDataAttributes {
        private String email;
    }

    @Getter
    @Setter
    private static class Metric {
        private MetricData data = new MetricData();

        public Metric(String name) {
            data.getAttributes().setName(name);
        }
    }

    @Getter
    @Setter
    private static class MetricData {
        private String type = "metric";
        private MetricDataAttributes attributes = new MetricDataAttributes();
    }

    @Getter
    @Setter
    private static class MetricDataAttributes {
        private String name;
    }

    @Getter
    @Setter
    private static class Properties {
        private String Subject;

        public Properties(String subject) {
            Subject = subject;
        }
    }
}
