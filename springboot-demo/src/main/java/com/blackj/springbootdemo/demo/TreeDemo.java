package com.blackj.springbootdemo.demo;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * Description
 */
public class TreeDemo {
    public static class TreeNode {
        private Integer id;
        private String name;
        private Integer parentId;
        private List<TreeNode> children = new ArrayList<>();

        public TreeNode(Integer id, String name, Integer parentId) {
            this.id = id;
            this.name = name;
            this.parentId = parentId;
        }

        public List<TreeNode> getChildren() {
            return children;
        }

        public Integer getId() {
            return id;
        }

        public String getName() {
            return name;
        }

        public Integer getParentId() {
            return parentId;
        }

        public void accept(Visitor visitor) {
            visitor.visit(this);
        }

        @Override
        public String toString() {
            return "TreeNode{" +
                    "id=" + id +
                    ", name='" + name + '\'' +
                    ", parentId=" + parentId +
                    ", children=" + children +
                    '}';
        }
    }

    interface Visitor {
        void visit(TreeNode treeNode);
    }

    public static class ChildrenVisitor implements Visitor {
        private final List<TreeNode> nodeList;

        public ChildrenVisitor(List<TreeNode> nodeList) {
            this.nodeList = nodeList;
        }

        @Override
        public void visit(TreeNode treeNode) {
            for (TreeNode node : nodeList) {
                if (node.getParentId().equals(treeNode.getId())) {
                    treeNode.getChildren().add(node);
                    node.accept(this);
                }
            }
        }
    }

    public static class RemoveVisitor implements Visitor {
        private List<Integer> removeIds;

        public RemoveVisitor(List<Integer> removeIds) {
            this.removeIds = removeIds;
        }

        @Override
        public void visit(TreeNode treeNode) {
            removeIds.add(treeNode.getId());
            if (treeNode.getChildren().size() > 0) {
                for (TreeNode subNode : treeNode.getChildren()) {
                    subNode.accept(this);
                }
            }
        }
    }

    public static void main(String[] args) {
        TreeNode parentNodeA = new TreeNode(1, "父节点A", 0);
        TreeNode childNodeA1 = new TreeNode(2, "子节点A-1", 1);
        TreeNode childNodeA2 = new TreeNode(3, "子节点A-2", 1);
        TreeNode leafNode1 = new TreeNode(4, "叶子节点1", 2);
        TreeNode leafNode2 = new TreeNode(5, "叶子节点2", 2);
        TreeNode leafNode3 = new TreeNode(6, "叶子节点3", 3);

        TreeNode parentNodeB = new TreeNode(7, "父节点B", 0);
        TreeNode childNodeB1 = new TreeNode(8, "子节点B-1", 7);
        TreeNode leafNode4 = new TreeNode(9, "叶子节点4", 8);

        List<TreeNode> nodeList = new ArrayList<>();
        nodeList.add(parentNodeA);
        nodeList.add(childNodeA1);
        nodeList.add(childNodeA2);
        nodeList.add(leafNode1);
        nodeList.add(leafNode2);
        nodeList.add(leafNode3);

        // 构造树
        Visitor childrenVisitor = new ChildrenVisitor(nodeList);
        parentNodeA.accept(childrenVisitor);
        System.out.println(parentNodeA);
        // 级联删除节点
        List<Integer> removeIds = new ArrayList<>();
        Visitor removeVisitor = new RemoveVisitor(removeIds);
        parentNodeA.accept(removeVisitor);
        System.out.println();
    }
}
