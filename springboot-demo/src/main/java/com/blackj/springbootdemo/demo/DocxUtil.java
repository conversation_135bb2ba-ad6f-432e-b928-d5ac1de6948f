package com.blackj.springbootdemo.demo;

import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;

import java.io.File;
import java.io.FileInputStream;
import java.util.HashMap;

/**
 * <AUTHOR>
 */
public class DocxUtil {
    public static void handleDocx() throws Exception {
        File file = new File("/Users/<USER>/Downloads/demo.docx");
        // 读取文档
        XWPFDocument doc = new XWPFDocument(new FileInputStream(file));
        for (XWPFParagraph paragraph : doc.getParagraphs()) {
            for (XWPFRun run : paragraph.getRuns()) {
                System.out.println(run.text());
            }
        }
    }
}
