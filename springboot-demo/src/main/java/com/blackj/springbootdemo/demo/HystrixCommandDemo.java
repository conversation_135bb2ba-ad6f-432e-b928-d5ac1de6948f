package com.blackj.springbootdemo.demo;

import com.alibaba.fastjson.JSON;
import com.blackj.springbootdemo.domain.User;
import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommandGroupKey;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * <AUTHOR>
 * Description Hystrix 线程池，资源隔离
 * 默认使用的线程是10个，也就是说最多就是10个线程会去调用服务。
 * 这样做的好处是如果用户服务出现故障，最多也就10个线程会被无效化，
 * 避免所有的线程资源全部耗费在这个故障服务上，可能导致其他所有服务都不可用。
 */
public class HystrixCommandDemo extends HystrixCommand<User> {
    private Long userId;

    public HystrixCommandDemo(Long userId) {
        super(HystrixCommandGroupKey.Factory.asKey("GetUserCommandGroup"));
        this.userId = userId;
    }

    @Override
    protected User run() throws Exception {
        String url = "http://localhost:8080/test/user/" + userId;
        // 调用获取用户接口
        OkHttpClient httpClient = new OkHttpClient();
        Request request = new Request.Builder().url(url).get().build();
        Response response = httpClient.newCall(request).execute();

        return JSON.parseObject(response.body().string(), User.class);
    }

    public static void main(String[] args) {
        Long userId = 2L;
        HystrixCommand<User> userCommand = new HystrixCommandDemo(userId);
        User user = userCommand.execute();
        System.out.println(user);
    }
}
