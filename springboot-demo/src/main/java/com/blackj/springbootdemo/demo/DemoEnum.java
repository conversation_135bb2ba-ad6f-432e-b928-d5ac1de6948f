package com.blackj.springbootdemo.demo;

import lombok.Getter;

import java.util.function.BiConsumer;

/**
 * <AUTHOR>
 */
@Getter
public enum DemoEnum {
    A("A", Entity::setA),
    B("B", Entity::setB);

    private final String field;
    private final BiConsumer<Entity, String> consumer;

    DemoEnum(String field, BiConsumer<Entity, String> consumer) {
        this.field = field;
        this.consumer = consumer;
    }

    public void execute(Entity entity, String value) {
        consumer.accept(entity, value);
    }
}
