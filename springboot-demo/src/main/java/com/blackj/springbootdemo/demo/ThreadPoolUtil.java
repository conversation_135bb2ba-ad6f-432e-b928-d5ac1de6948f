package com.blackj.springbootdemo.demo;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 */
public class ThreadPoolUtil {
    private static int coreSize = Runtime.getRuntime().availableProcessors() + 1;
    private ThreadPoolExecutor threadPoolExecutor;

    private ThreadPoolUtil() {
        threadPoolExecutor = new ThreadPoolExecutor(
                coreSize,
                coreSize,
                0L,
                TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200),
                new ThreadPoolExecutor.CallerRunsPolicy());
    }

    private static class ThreadPoolHolder {
        private static ThreadPoolUtil instance = new ThreadPoolUtil();
    }

    public static ThreadPoolExecutor getPool() {
        return ThreadPoolHolder.instance.threadPoolExecutor;
    }
}
