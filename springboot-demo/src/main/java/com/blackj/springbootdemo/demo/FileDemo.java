package com.blackj.springbootdemo.demo;

import java.io.*;
import java.util.ArrayDeque;
import java.util.Arrays;

/**
 * <AUTHOR>
 * Description
 */
public class FileDemo {
    public static void main(String[] args) throws Exception {
        String filePath = "/Users/<USER>/area.txt";
        File file = new File(filePath);
        BufferedReader bufferedReader = new BufferedReader(new FileReader(file));

        ArrayDeque<String[]> lineDeque = new ArrayDeque<>();
        String line;
        while ((line = bufferedReader.readLine()) != null) {
            if (line.contains("全部区")) {
                lineDeque.addLast(line.split("\t"));
            }
        }

        ArrayDeque<String> sqlDeque = new ArrayDeque<>();
        for (String[] arr : lineDeque) {
            String areaCode = arr[5].substring(0, 6);
            String parentCode = arr[3].substring(0, 6);
            sqlDeque.addLast("insert into sys_area "
                    .concat("(area_name,area_code,parent_code,`level`) ")
                    .concat("select ")
                    .concat("'市本级',")
                    .concat(areaCode.concat(","))
                    .concat(parentCode.concat(","))
                    .concat("3 ")
                    .concat("where (select count(*) from sys_area where area_code=" + areaCode + ")=0;"));
        }

        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter("/Users/<USER>/area_sql.txt"));
        bufferedWriter.write("begin;");
        bufferedWriter.newLine();
        for (String sql : sqlDeque) {
            bufferedWriter.write(sql);
            bufferedWriter.newLine();
        }
        bufferedWriter.write("commit;");

        bufferedReader.close();
        bufferedWriter.close();
    }
}
