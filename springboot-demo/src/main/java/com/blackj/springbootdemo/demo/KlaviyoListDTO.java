package com.blackj.springbootdemo.demo;

import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class KlaviyoListDTO {
    private List<Profile> data;
    private String desc;

    public KlaviyoListDTO(String id) {
        this.data = Collections.singletonList(new Profile(id));
    }

    @Getter
    @Setter
    private static class Profile {
        private String type = "profile";
        private String id;
        private String email;

        public Profile(String id) {
            this.id = id;
        }
    }
}
