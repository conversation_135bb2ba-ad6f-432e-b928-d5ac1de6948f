package com.blackj.springbootdemo.demo;

import org.redisson.RedissonLock;
import org.redisson.RedissonWriteLock;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * Description
 */
public class RedissonDemo {
    private RedissonClient redissonClient;

    public void lock() {
        RLock rLock = redissonClient.getLock("ABC");
        try {
            boolean b = rLock.tryLock(1, TimeUnit.SECONDS);
        } catch (InterruptedException ex) {
            ex.printStackTrace();
        } finally {
            rLock.unlock();
        }
    }
}
