package com.blackj.springbootdemo.demo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;

import java.io.*;
import java.util.*;

/**
 * <AUTHOR>
 * Description
 */
public class FileDemo2 {
    public static void main(String[] args) throws Exception {
        String pk1 = "*************************************";
        String pk2 = "*************************************";
        String pk3 = "*************************************";
        String pk4 = "*************************************";
        String pk5 = "*************************************";

        String filePath = "/Users/<USER>/IdeaProjects/mira_server/~/Desktop/server_logs/consumer/error.2022-08-24.log";
        File file = new File(filePath);
        BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter("/Users/<USER>/tmp/tmp.txt"));

        String line;
        while ((line = bufferedReader.readLine()) != null) {
            if (line.contains(pk1) || line.contains(pk2)
                    || line.contains(pk3) || line.contains(pk4)
                    || line.contains(pk5)) {
//                bufferedWriter.write(line.substring(0, line.indexOf(",")));
                bufferedWriter.write(line);
                bufferedWriter.newLine();
            }
        }

        bufferedReader.close();
        bufferedWriter.close();
    }

    static class AreaJson {
        private String id;
        private String name;
        private String parentId;
        private String cityName;
        private List<AreaJson> subarea;

        public String getId() {
            return id;
        }

        public void setId(String id) {
            this.id = id;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getParentId() {
            return parentId;
        }

        public void setParentId(String parentId) {
            this.parentId = parentId;
        }

        public String getCityName() {
            return cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }

        public List<AreaJson> getSubarea() {
            return subarea;
        }

        public void setSubarea(List<AreaJson> subarea) {
            this.subarea = subarea;
        }
    }
}
