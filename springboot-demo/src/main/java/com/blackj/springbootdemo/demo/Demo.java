package com.blackj.springbootdemo.demo;

import cn.hutool.crypto.digest.MD5;
import cn.hutool.json.JSONUtil;
import com.blackj.springbootdemo.utils.*;
import com.google.common.base.Joiner;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;

import java.net.URI;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class Demo {

    public static void main(String[] args) throws Exception {
//        System.out.println(UUID.randomUUID().toString().replaceAll("-", ""));
//        System.out.println(PasswordUtil.encryptPassword("b6fe10e0676f47a8a79c3c12ca336dba3e7478fbae85457abf4d0e766377bc29", null));
        String encode = "eYG8TCXATA38mua+bIeakfv0cjYI32eX+kXUfxeYbME7F7We7yedBC+UGbu7kBw8V/kfBu9bEvAYh0lhClzkVAFVB9MU3/hCJVfJzAbzHkg+jATJLxG9WLB3btW8VYKfjv7BEbWjb2t9MeLze1FamfYcfmHVFffAjaZ4VTL/A4j";
        String decode = HashDecodeUtil.decodeHash(encode);
        String s = RsaUtil.decodeRsa(decode, "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBALcV2Ul2uC1xE/GjBPzR8EQS2xyb7aKftRV0hQtOc+/CNau+2+3MPMbTYBbYfPvfEqTAoXuRdfu4q9Y708OgLf+eMnr+MVqJMC/t9jXzyTELo7sPEYI88n48F1g7qwCHGYkQiOw8VHOuy6Vflt3Px6An/xeI51nphF9QUKcVgr93AgMBAAECgYApiioGOor4OHdLdG7dw+5NK7AOMiy9IJpP92N2WMMCS6Csyn2Q2y2Q+QWMuWrGJOtQeSpKZIsN5W0LiwB1msLEobB2FILOKxnUTwDg8lQ1k4ms2Airlcj1N531DpNvl8eP9XsaGP1vAMX7k3mSQLMvtfipT7Z4HRgvb7WblLfQfQJBAOMIOfsrl1kcbu3KQVJBtyzFRd4jK2F1t3mHZMKABvT473WACOcqzIzC4To3SpEChHdCMKkHiGgLh27qt0AV0GsCQQDOciUsXHYetuPS5derWTeTCxxVXFvk2jf5XizhiLuWf2X20IdTjWwchED3Sn1d9WbfNRFbt0s5lpWMG217euAlAkAxQdiqmROWPugGaYpwmVdvvEFjZ8gCtjKfiHOhC3v27Ievu56y4QNOSv3ozpQ8EArDgTXjhoHe6RlyRDSMD++PAkEAqlsG0dR6pF3HIgEINx0n/u7sMoREUFTCW8SaxIFQSqBWQ5VQFmJdFCsOcQHG8gHPIeFosYZPTPY18aewcHS1AQJBAMtFhqEKO+1Nav7sdOYZhMhlhy4ncUNpWu+yZNNBF3znL0Nq4Quh8iouwpt9NszZCVT8neXda8X6s5yHq8rVyXA=");
        System.out.println(s);
    }
}
