package com.blackj.springbootdemo.demo;

import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BrokenBarrierException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CyclicBarrier;

/**
 * <AUTHOR>
 */
public class CyclicBarrierDemo {
    public static void main(String[] args) throws Exception {
        Long[] userIdArr = {11L, 12L, 13L, 14L, 15L,
                21L, 22L, 23L, 24L, 25L,
                31L, 32L, 33L, 34L, 35L,
                41L, 42L, 43L, 44L, 45L,
                51L, 52L, 53L};
        List<Long> userIdList = Lists.newArrayList(userIdArr);

        int N = 5;
        CompletableFuture<Void> all = new CompletableFuture<>();

        int cycle = 0;
        for (int i = 0; i < userIdList.size(); i++) {
            // 添加任务
            final int finalI = i;
            all = CompletableFuture.allOf(CompletableFuture.runAsync(() -> {
                try {
                    Thread.sleep(1000);
                    System.out.println(Thread.currentThread().getName() + "，开始执行用户的任务" + userIdList.get(finalI));
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            }));

            cycle++;
            if (cycle == N || i == userIdList.size() - 1) {
                all.join();
                cycle = 0;
            }
        }

        System.out.println("Finished");
    }
}
