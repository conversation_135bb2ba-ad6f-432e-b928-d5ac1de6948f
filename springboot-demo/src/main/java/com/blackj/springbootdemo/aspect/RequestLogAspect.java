package com.blackj.springbootdemo.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.blackj.springbootdemo.annotation.RequestLog;
import com.blackj.springbootdemo.spring.ApplicationContextUtil;
import com.blackj.springbootdemo.spring.RequestLogEvent;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Map;

/**
 * <AUTHOR>
 * Description
 */
@Aspect
@Component
@Slf4j
public class RequestLogAspect {
    @Pointcut("@annotation(requestLog)")
    public void requestLogPoint(RequestLog requestLog) {
    }

    @Around(value = "requestLogPoint(requestLog)", argNames = "joinPoint,requestLog")
    public Object around(ProceedingJoinPoint joinPoint, RequestLog requestLog) throws Throwable {
        Object result = joinPoint.proceed();
        System.out.println("当前线程：" + Thread.currentThread().getName());

        ApplicationContextUtil.publishEvent(new RequestLogEvent(joinPoint, result));

        return result;
    }
}
