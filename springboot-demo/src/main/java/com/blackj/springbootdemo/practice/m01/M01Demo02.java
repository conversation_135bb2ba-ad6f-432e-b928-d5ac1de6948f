package com.blackj.springbootdemo.practice.m01;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * <AUTHOR>
 * @description JDK 动态代理
 */
public class M01Demo02 {
    /**
     * 短信接口
     */
    interface SendMsg {
        void send();
    }

    /**
     * 短信类
     */
    static class SendMsgImpl implements SendMsg {
        public void send() {
            System.out.println("发送短信");
        }
    }

    /**
     * 动态代理，日志打印类
     */
    static class LogInvocationHandler implements InvocationHandler {
        /**
         * 实际类
         */
        private final Object target;

        public LogInvocationHandler(Object target) {
            this.target = target;
        }

        @Override
        public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
            System.out.println("记录发送短信参数");
            Object result = method.invoke(target, args);
            System.out.println("打印是否成功发送");
            return result;
        }
    }

    /**
     * 动态代理，工厂类
     */
    static class LogProxyFactory {
        public static Object getProxy(Object target) {
            return Proxy.newProxyInstance(
                    target.getClass().getClassLoader(),
                    target.getClass().getInterfaces(),
                    new LogInvocationHandler(target));
        }
    }

    public static void main(String[] args) {
        SendMsg sendMsg = (SendMsg) LogProxyFactory.getProxy(new SendMsgImpl());
        sendMsg.send();
    }
}
