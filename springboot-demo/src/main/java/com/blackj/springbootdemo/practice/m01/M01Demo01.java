package com.blackj.springbootdemo.practice.m01;

/**
 * <AUTHOR>
 * @description
 */
public class M01Demo01 {
    static class Address implements Cloneable {
        @Override
        protected Address clone() throws CloneNotSupportedException {
            return (Address) super.clone();
        }
    }

    static class Person implements Cloneable {
        private Address address;
        public Person(Address address) {
            this.address = address;
        }

        public Address getAddress() {
            return this.address;
        }

        public void setAddress(Address address) {
            this.address = address;
        }

        // 浅拷贝
//        @Override
//        protected Person clone() throws CloneNotSupportedException {
//            return (Person) super.clone();
//        }

        // 深拷贝
        @Override
        protected Person clone() throws CloneNotSupportedException {
            Person person = (Person) super.clone();
            person.setAddress(person.getAddress().clone());
            return person;
        }
    }

    public static void main(String[] args) throws Exception {
        // 浅拷贝
        Person person = new Person(new Address());
        Person person1 = person.clone();

        System.out.println("浅拷贝: " + (person.getAddress() == person1.getAddress()));

        Person person_ = new Person(new Address());
        Person person_1 = person_.clone();
        System.out.println("深拷贝：" + (person_.getAddress() == person_1.getAddress()));
    }
}
