package com.blackj.springbootdemo.practice.m01;

import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;

/**
 * <AUTHOR>
 * @description Cglib 动态代理
 */
public class M01Demo03 {
    /**
     * 发送短信类
     */
    static class SendMsg {
        public void send() {
            System.out.println("发送短信");
        }
    }

    /**
     * 拦截器
     */
    static class LogInterceptor implements MethodInterceptor {
        /**
         * @param o 代理的对象
         * @param method 被拦截的方法
         * @param args 方法入参
         * @param methodProxy 用于调用原始方法
         * @return
         * @throws Throwable
         */
        @Override
        public Object intercept(Object o, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
            System.out.println("记录发送短信参数");
            Object result = methodProxy.invokeSuper(o, args);
            System.out.println("打印是否成功发送");
            return result;
        }
    }

    /**
     * 工厂类
     */
    static class LogProxyFactory {
        public static Object getProxy(Class<?> clazz) {
            Enhancer enhancer = new Enhancer();
            enhancer.setClassLoader(clazz.getClassLoader());
            enhancer.setSuperclass(clazz);
            enhancer.setCallback(new LogInterceptor());
            return enhancer.create();
        }
    }

    public static void main(String[] args) {
        SendMsg sendMsg = (SendMsg) LogProxyFactory.getProxy(SendMsg.class);
        sendMsg.send();
    }
}
