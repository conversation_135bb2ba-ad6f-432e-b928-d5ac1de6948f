package com.blackj.springbootdemo.jwt;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * jwt 工具类
 *
 * <AUTHOR>
 */
public class JwtUtil {
    /**
     * 生成 JWT，包含额外信息
     *
     * @param payload      额外信息
     * @param expireSecond 过期时间（秒）
     * @param signKey      签名密钥
     * @return JWT
     */
    public static String createJwt(Map<String, Object> payload, long expireSecond, String signKey) {
        long now = System.currentTimeMillis();
        return Jwts.builder()
                .setClaims(payload)
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setIssuedAt(new Date(now))
                .setExpiration(new Date(now + expireSecond * 1000))
                .signWith(generateKey(signKey))
                .compact();
    }

    /**
     * 生成 JWT，包含额外信息
     *
     * @param payload      额外信息
     * @param signKey      签名密钥
     * @return JWT
     */
    public static String createJwt(Map<String, Object> payload, String signKey) {
        long now = System.currentTimeMillis();
        return Jwts.builder()
                .setClaims(payload)
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
//                .setIssuedAt(new Date(now))
                .signWith(generateKey(signKey))
                .compact();
    }

    /**
     * 生成 JWT
     *
     * @param expireSecond 过期时间（秒）
     * @param signKey      签名密钥
     * @return JWT
     */
    public static String createJwt(long expireSecond, String signKey) {
        long now = System.currentTimeMillis();
        return Jwts.builder()
                .setHeaderParam("typ", "JWT")
                .setHeaderParam("alg", "HS256")
                .setIssuedAt(new Date(now))
                .setExpiration(new Date(now + expireSecond * 1000))
                .signWith(generateKey(signKey))
                .compact();
    }

    /**
     * 解析 JWT
     *
     * @param jwt JWT
     * @return Claims
     */
    public static Claims parseClaims(String jwt, String signKey) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(generateKey(signKey))
                    .build()
                    .parseClaimsJws(jwt)
                    .getBody();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 验证 JWT
     *
     * @param claims Claims
     * @return true/false
     */
    public static boolean isExpired(Claims claims) {
        return System.currentTimeMillis() > claims.getExpiration().getTime();
    }

    private static SecretKey generateKey(String signKey) {
        return new SecretKeySpec(signKey.getBytes(StandardCharsets.UTF_8), SignatureAlgorithm.HS256.getJcaName());
    }
}
