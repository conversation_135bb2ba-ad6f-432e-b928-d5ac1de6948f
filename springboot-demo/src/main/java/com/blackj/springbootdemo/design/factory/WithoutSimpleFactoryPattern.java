package com.blackj.springbootdemo.design.factory;

/**
 * <AUTHOR>
 * Description 不使用工厂设计模式
 */
public class WithoutSimpleFactoryPattern {
    /**
     * 产品接口
     */
    public interface Product {
        void print();
    }

    /**
     * 产品类
     */
    public static class ProductA implements Product {
        @Override
        public void print() {
            System.out.println("产品A");
        }
    }

    public static void main(String[] args) {
        // 创建一个产品
        Product product = new ProductA();
        product.print();
        // 如果Product改了名字或需要换成其他类，那么所以new Product的地方都需要更改
    }
}
