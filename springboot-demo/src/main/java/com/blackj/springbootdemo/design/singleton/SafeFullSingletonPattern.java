package com.blackj.springbootdemo.design.singleton;

/**
 * <AUTHOR>
 * Description 线程安全的饱汉单例模式
 * 因为不同 JVM 编译器的问题，这个也是可能线程不安全的
 */
public class SafeFullSingletonPattern {
    public static class Singleton {
        private volatile static Singleton INSTANCE;

        private Singleton() {}

        public static Singleton getInstance() {
            // 加锁，双重检查
            if (INSTANCE == null) {
                synchronized(Singleton.class) {
                    if (INSTANCE == null) {
                        INSTANCE = new Singleton();
                    }
                }
            }
            return INSTANCE;
        }
    }
}
