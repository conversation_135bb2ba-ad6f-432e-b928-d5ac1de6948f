package com.blackj.springbootdemo.design.composite;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Description
 */
public class CompositePattern {
    /**
     * 部门类
     */
    public static class Department {
        private String name;
        private List<Department> children = new ArrayList<>();

        public Department(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public List<Department> getChildren() {
            return children;
        }

        public void print() {
            System.out.println("部门【" + getName() + "】");
            if (getChildren().size() > 0) {
                for (Department dept : getChildren()) {
                    dept.print();
                }
            }
        }
    }

    public static void main(String[] args) {
        // 创建部门
        Department leafDeptA = new Department("叶子部门A");
        Department leafDeptB = new Department("叶子部门B");
        Department leafDeptC = new Department("叶子部门C");

        Department subDeptA = new Department("子部门A");
        Department subDeptB = new Department("子部门B");

        Department parentDept = new Department("父部门");

        // 级联部门
        subDeptA.getChildren().add(leafDeptA);
        subDeptA.getChildren().add(leafDeptB);
        subDeptB.getChildren().add(leafDeptC);
        parentDept.getChildren().add(subDeptA);
        parentDept.getChildren().add(subDeptB);

        // 级联打印
        parentDept.print();
    }
}
