package com.blackj.springbootdemo.design.decorator;

/**
 * <AUTHOR>
 * Description 装饰器模式
 */
public class DecoratorPattern {
    interface Component {
        void execute();
    }

    public static class ConcreteComponent implements Component {
        @Override
        public void execute() {
            System.out.println("执行基础功能");
        }
    }

    public static class Decorator {
        private ConcreteComponent component;

        public Decorator(ConcreteComponent component) {
            this.component = component;
        }

        public void execute() {
            System.out.println("执行基础功能前执行增强功能");
            component.execute();
            System.out.println("执行基础功能后执行增强功能");
        }
    }

    public static void main(String[] args) {
        Decorator decorator = new Decorator(new ConcreteComponent());
        decorator.execute();
    }
}
