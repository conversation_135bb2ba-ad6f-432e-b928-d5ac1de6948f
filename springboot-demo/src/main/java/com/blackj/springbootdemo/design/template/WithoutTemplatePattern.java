package com.blackj.springbootdemo.design.template;

/**
 * <AUTHOR>
 * Description 不使用模版方法设计模式
 */
public class WithoutTemplatePattern {
    public static class DiscountCalculate1 {
        public void calculate() {
            System.out.println("优惠券通用计算公式");
            System.out.println("特殊计算公式1");
        }
    }

    public static class DiscountCalculate2 {
        public void calculate() {
            System.out.println("优惠券通用计算公式");
            System.out.println("特殊计算公式2");
        }
    }

    public static class DiscountCalculate3 {
        public void calculate() {
            System.out.println("优惠券通用计算公式");
            System.out.println("特殊计算公式3");
        }
    }

    public static void main(String[] args) {
        /*
        三种优惠券里有相同的计算功能，如果修改通用计算公式的话，
        就需要把三种优惠券里的通用计算代码都改一遍。
         */
        DiscountCalculate1 discountCalculate1 = new DiscountCalculate1();
        discountCalculate1.calculate();

        DiscountCalculate2 discountCalculate2 = new DiscountCalculate2();
        discountCalculate2.calculate();

        DiscountCalculate3 discountCalculate3 = new DiscountCalculate3();
        discountCalculate3.calculate();
    }
}
