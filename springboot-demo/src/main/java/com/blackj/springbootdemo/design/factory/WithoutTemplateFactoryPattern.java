package com.blackj.springbootdemo.design.factory;

/**
 * <AUTHOR>
 * Description 不使用模版工厂方法
 */
public class WithoutTemplateFactoryPattern {
    public interface Product {
        void print();
    }

    /**
     * 产品A
     */
    public static class ProductA implements Product {
        @Override
        public void print() {
            System.out.println("产品A");
        }
    }

    /**
     * 产品B
     */
    public static class ProductB implements Product {
        @Override
        public void print() {
            System.out.println("产品B");
        }
    }

    /**
     * 产品A工厂
     */
    public static class ProductAFactory {
        public static Product createProduct() {
            System.out.println("生产产品的通用逻辑");
            System.out.println("产品A的特殊逻辑");
            return new ProductA();
        }
    }

    /**
     * 产品B工厂
     */
    public static class ProductBFactory {
        public static Product createProduct() {
            System.out.println("生产产品的通用逻辑");
            System.out.println("产品B的特殊逻辑");
            return new ProductB();
        }
    }

    public static void main(String[] args) {
        // 如果要修改通用产品逻辑，就需要修改所有涉及到的地方
        Product productA = ProductAFactory.createProduct();
        Product productB = ProductBFactory.createProduct();

        productA.print();
        productB.print();
    }
}
