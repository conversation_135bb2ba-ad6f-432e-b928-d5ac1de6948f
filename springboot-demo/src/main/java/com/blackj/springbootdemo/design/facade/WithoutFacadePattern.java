package com.blackj.springbootdemo.design.facade;

/**
 * <AUTHOR>
 * Description 不使用门面模式
 */
public class WithoutFacadePattern {
    public static class ModuleA {
        public void execute() {
            System.out.println("系统A的A模块功能");
        }
    }

    public static class ModuleB {
        public void execute() {
            System.out.println("系统A的B模块功能");
        }
    }

    public static class ModuleC {
        public void execute() {
            System.out.println("系统A的C模块功能");
        }
    }

    public static void main(String[] args) {
        // 这里是系统B，需要调用系统A的3个模块的功能
        ModuleA moduleA = new ModuleA();
        ModuleB moduleB = new ModuleB();
        ModuleC moduleC = new ModuleC();

        moduleA.execute();
        moduleB.execute();
        moduleC.execute();
    }
}
