package com.blackj.springbootdemo.design.mediator;

/**
 * <AUTHOR>
 * Description 不使用中介者（调停者）模式
 */
public class WithoutMediatorPattern {
    public static class ModuleA {
        public void execute() {
            // 创建模块B和C
            ModuleB moduleB = new ModuleB();
            ModuleC moduleC = new ModuleC();
            moduleB.execute("模块A");
            moduleC.execute("模块A");
        }

        public void execute(String invoker) {
            System.out.println(invoker + "调用模块A的功能");
        }
    }

    public static class ModuleB {
        public void execute() {
            // 创建模块A和模块C
            ModuleA moduleA = new ModuleA();
            ModuleC moduleC = new ModuleC();
            moduleA.execute("模块B");
            moduleC.execute("模块B");
        }

        public void execute(String invoker) {
            System.out.println(invoker + "调用模块B的功能");
        }
    }

    public static class ModuleC {
        public void execute() {
            // 创建模块A和模块B
            ModuleA moduleA = new ModuleA();
            ModuleB moduleB = new ModuleB();
            moduleA.execute("模块C");
            moduleB.execute("模块C");
        }

        public void execute(String invoker) {
            System.out.println(invoker + "调用模块C的功能");
        }
    }

    public static void main(String[] args) {
        // 模块之间相互调用
        // 比如模块A调用模块B、模块C，模块B调用模块A、模块C，模块C调用模块A、模块B
        ModuleA moduleA = new ModuleA();
        ModuleB moduleB = new ModuleB();
        ModuleC moduleC = new ModuleC();

        moduleA.execute();
        moduleB.execute();
        moduleC.execute();
    }
}
