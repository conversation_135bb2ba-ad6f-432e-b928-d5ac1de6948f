package com.blackj.springbootdemo.design.singleton;

/**
 * <AUTHOR>
 * Description 线程不安全的饱汉单例模式
 */
public class UnsafeFullSingletonPattern {
    public static class Singleton {
        private static Singleton INSTANCE;

        private Singleton() {}

        public static Singleton getInstance() {
            if (INSTANCE == null) {
                INSTANCE = new Singleton();
            }
            return INSTANCE;
        }
    }
}
