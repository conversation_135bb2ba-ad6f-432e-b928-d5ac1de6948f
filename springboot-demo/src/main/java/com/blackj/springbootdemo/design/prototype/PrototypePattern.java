package com.blackj.springbootdemo.design.prototype;

/**
 * <AUTHOR>
 * Description 原型模式
 */
public class PrototypePattern {
    public static class Component {
        private String name;

        public Component(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        @Override
        protected Object clone() throws CloneNotSupportedException {
            return new Component(getName());
        }

        @Override
        public String toString() {
            return "Component{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    public static class Product {
        private String name;
        private Component component;

        public Product(String name, Component component) {
            this.name = name;
            this.component = component;
        }

        public String getName() {
            return name;
        }

        public Component getComponent() {
            return component;
        }

        @Override
        protected Object clone() throws CloneNotSupportedException {
            // 深拷贝
            return new Product(getName(), (Component) getComponent().clone());
        }

        @Override
        public String toString() {
            return "Product{" +
                    "name='" + name + '\'' +
                    ", component=" + component +
                    '}';
        }
    }

    public static void main(String[] args) {
        Product product = new Product("测试产品", new Component("组件"));
        // 拷贝对象
        try {
            Product copyProduct = (Product) product.clone();
            System.out.println(product);
            System.out.println(copyProduct);
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
    }
}
