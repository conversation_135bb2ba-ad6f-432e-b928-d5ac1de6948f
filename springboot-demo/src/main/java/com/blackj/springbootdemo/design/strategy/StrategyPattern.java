package com.blackj.springbootdemo.design.strategy;

/**
 * <AUTHOR>
 * Description 策略模式
 * 根据参数不同，选择不同的优惠计价策略
 */
public class StrategyPattern {
    interface DiscountStrategy {
        void calculate();
    }

    public static class DiscountStrategyA implements DiscountStrategy {
        @Override
        public void calculate() {
            System.out.println("执行第一种优惠计价方式");
        }
    }

    public static class DiscountStrategyB implements DiscountStrategy {
        @Override
        public void calculate() {
            System.out.println("执行第二种优惠计价方式");
        }
    }

    public static class DiscountStrategyDefault implements DiscountStrategy {
        @Override
        public void calculate() {
            System.out.println("执行默认的优惠计价方式");
        }
    }

    /**
     * 策略工厂类
     */
    public static class DiscountStrategyFactory {
        public static DiscountStrategy getDiscountStrategy(int discountStyle) {
            if (discountStyle == 1) {
                return new DiscountStrategyA();
            } else if (discountStyle == 2) {
                return new DiscountStrategyB();
            } else {
                return new DiscountStrategyDefault();
            }
        }
    }

    public static void main(String[] args) {
        // 根据参数不同，执行不同的优惠计价方式
        int discountStyle = 1;

        DiscountStrategy discountStrategy = DiscountStrategyFactory.getDiscountStrategy(discountStyle);
        discountStrategy.calculate();
    }
}
