package com.blackj.springbootdemo.design.singleton;

/**
 * <AUTHOR>
 * Description 内部类实现的单例模式，这是完美的单例模式
 * 开发中用到单例模式都用这种方式
 */
public class InnerFullSingletonPatter {
    /**
     * 内部类，只要没有被使用就不会初始化
     * 第一次调用getInstance()的时候初始化内部类
     * JVM 保证类静态初始化的过程只会执行一次
     */
    public static class Singleton {
        private Singleton() {}

        private static class InnerHolder {
            private final static Singleton INSTANCE = new Singleton();
        }

        public static Singleton getInstance() {
            return InnerHolder.INSTANCE;
        }
    }
}
