package com.blackj.springbootdemo.design.factory;

/**
 * <AUTHOR>
 * Description 模版工厂模式
 */
public class TemplateFactoryPattern {
    public interface Product {
        void print();
    }

    /**
     * 产品A
     */
    public static class ProductA implements Product {
        @Override
        public void print() {
            System.out.println("产品A");
        }
    }

    /**
     * 产品B
     */
    public static class ProductB implements Product {
        @Override
        public void print() {
            System.out.println("产品B");
        }
    }

    public static abstract class AbstractTemplateFactory {
        public Product createProduct() {
            common();
            return special();
        }

        /**
         * 通用生产逻辑
         */
        private void common() {
            System.out.println("生产产品的通用逻辑");
        }

        /**
         * 特殊生产逻辑
         */
        protected abstract Product special();
    }

    public static class ProductAFactory extends AbstractTemplateFactory {
        private final static ProductAFactory INSTANCE = new ProductAFactory();

        @Override
        protected Product special() {
            System.out.println("产品A的特殊逻辑");
            return new ProductA();
        }

        public static ProductAFactory getInstance() {
            return INSTANCE;
        }
    }

    public static class ProductBFactory extends AbstractTemplateFactory {
        private final static ProductBFactory INSTANCE = new ProductBFactory();

        @Override
        protected Product special() {
            System.out.println("产品B的特殊逻辑");
            return new ProductB();
        }

        public static ProductBFactory getInstance() {
            return INSTANCE;
        }
    }

    public static void main(String[] args) {
        Product productA = ProductAFactory.getInstance().createProduct();
        Product productB = ProductBFactory.getInstance().createProduct();

        productA.print();
        productB.print();
    }
}
