package com.blackj.springbootdemo.design.bridge;

/**
 * <AUTHOR>
 * Description 桥接模式
 */
public class BridgePattern {
    interface Service {
        void execute();
    }

    public static class UserServiceImpl implements Service {
        @Override
        public void execute() {
            System.out.println("查询用户");
        }
    }

    public static class RoleServiceImpl implements Service {
        @Override
        public void execute() {
            System.out.println("查询角色");
        }
    }

    public static class BridgeAction {
        private Service service;

        public BridgeAction(Service service) {
            this.service = service;
        }

        public void execute() {
            service.execute();
        }
    }

    public static void main(String[] args) {
        // 桥接模式其实就是面向接口编程，java中无处不桥接
        // 一个组件面向其他组件的接口来编程，这个接口也就相当于用来连接的一座桥
        BridgeAction userAction = new BridgeAction(new UserServiceImpl());
        userAction.execute();

        BridgeAction roleAction = new BridgeAction(new RoleServiceImpl());
        roleAction.execute();
    }
}
