package com.blackj.springbootdemo.design.mediator;

/**
 * <AUTHOR>
 * Description 使用中介者（调停者）模式
 */
public class MediatorPattern {
    /**
     * 中介者
     */
    public static class Mediator {
        private ModuleA moduleA;
        private ModuleB moduleB;
        private ModuleC moduleC;

        public void setModuleA(ModuleA moduleA) {
            this.moduleA = moduleA;
        }

        public void setModuleB(ModuleB moduleB) {
            this.moduleB = moduleB;
        }

        public void setModuleC(ModuleC moduleC) {
            this.moduleC = moduleC;
        }

        public void moduleAInvoke() {
            // 想要修改模块A调用的模块功能时，修改这里就可以了，这就达到了解耦的效果
            moduleB.execute("模块A");
            moduleC.execute("模块A");
        }

        public void moduleBInvoke() {
            moduleA.execute("模块B");
            moduleC.execute("模块B");
        }

        public void moduleCInvoke() {
            moduleA.execute("模块C");
            moduleB.execute("模块C");
        }
    }

    public static class ModuleA {
        private final Mediator mediator;

        public ModuleA(Mediator mediator) {
            // 创建模块时，将自身放入中介者中
            this.mediator = mediator;
            mediator.setModuleA(this);
        }

        public void execute() {
            mediator.moduleAInvoke();
        }

        public void execute(String invoker) {
            System.out.println(invoker + "调用模块A的功能");
        }
    }

    public static class ModuleB {
        private final Mediator mediator;

        public ModuleB(Mediator mediator) {
            this.mediator = mediator;
            mediator.setModuleB(this);
        }

        public void execute() {
            mediator.moduleBInvoke();
        }

        public void execute(String invoker) {
            System.out.println(invoker + "调用模块B的功能");
        }
    }

    public static class ModuleC {
        private final Mediator mediator;

        public ModuleC(Mediator mediator) {
            this.mediator = mediator;
            mediator.setModuleC(this);
        }

        public void execute() {
            mediator.moduleCInvoke();
        }

        public void execute(String invoker) {
            System.out.println(invoker + "调用模块C的功能");
        }
    }

    public static void main(String[] args) {
        // 创建中介者
        Mediator mediator = new Mediator();
        // 创建模块
        ModuleA moduleA = new ModuleA(mediator);
        ModuleB moduleB = new ModuleB(mediator);
        ModuleC moduleC = new ModuleC(mediator);

        moduleA.execute();
        moduleB.execute();
        moduleC.execute();
    }
}
