package com.blackj.springbootdemo.design.factory;

/**
 * <AUTHOR>
 * Description 简单工厂模式
 */
public class SimpleFactoryPattern {
    /**
     * 产品接口
     */
    public interface Product {
        void print();
    }

    /**
     * A产品
     */
    public static class ProductA implements Product {
        @Override
        public void print() {
            System.out.println("产品A");
        }
    }

    /**
     * B产品
     */
    public static class ProductB implements Product {
        @Override
        public void print() {
            System.out.println("产品B");
        }
    }

    /**
     * 工厂类
     */
    public static class ProductFactory {
        public static Product create() {
            return new ProductA();
        }
    }

    public static void main(String[] args) {
        /*
        如果要修改成其他产品，并且100个地方都用到这段代码，这里就只需修改工厂类的实现即可。
         */
        Product product = ProductFactory.create();
        product.print();
    }
}
