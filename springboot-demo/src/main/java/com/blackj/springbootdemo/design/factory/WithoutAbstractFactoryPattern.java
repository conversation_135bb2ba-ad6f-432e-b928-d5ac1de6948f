package com.blackj.springbootdemo.design.factory;

/**
 * <AUTHOR>
 * Description 不使用抽象工厂
 */
public class WithoutAbstractFactoryPattern {
    /**
     * 产品A接口
     */
    public interface ProductA {
        void print();
    }

    public static class ProductA1 implements ProductA {
        @Override
        public void print() {
            System.out.println("产品A1");
        }
    }

    public static class ProductA2 implements ProductA {
        @Override
        public void print() {
            System.out.println("产品A2");
        }
    }

    /**
     * 产品B接口
     */
    public interface ProductB {
        void print();
    }

    public static class ProductB1 implements ProductB {
        @Override
        public void print() {
            System.out.println("产品B1");
        }
    }

    public static class ProductB2 implements ProductB {
        @Override
        public void print() {
            System.out.println("产品B2");
        }
    }

    public static void main(String[] args) {
        // 产品A1+B1组合
        ProductA productA1 = new ProductA1();
        ProductB productB1 = new ProductB1();
        productA1.print();
        productB1.print();

        // 产品A2+B2组合
        ProductA productA2 = new ProductA2();
        ProductB productB2 = new ProductB2();
        productA2.print();
        productB2.print();
    }
}
