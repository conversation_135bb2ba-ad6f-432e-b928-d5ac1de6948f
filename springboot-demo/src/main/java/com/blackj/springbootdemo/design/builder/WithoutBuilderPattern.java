package com.blackj.springbootdemo.design.builder;

/**
 * <AUTHOR>
 * Description 不使用构造器模式
 */
public class WithoutBuilderPattern {
    public static class Product {
        private String field1;
        private String field2;
        private String field3;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }

        public String getField3() {
            return field3;
        }

        public void setField3(String field3) {
            this.field3 = field3;
        }

        @Override
        public String toString() {
            return "Product{" +
                    "field1='" + field1 + '\'' +
                    ", field2='" + field2 + '\'' +
                    ", field3='" + field3 + '\'' +
                    '}';
        }
    }

    public static void main(String[] args) {
        // 创建一个产品
        Product product = new Product();
        System.out.println("对 field1 属性进行复杂校验");
        product.setField1("值1");
        System.out.println("对 field2 属性进行格式化");
        product.setField2("值2");
        System.out.println("对 field3 属性进行复杂逻辑处理");
        product.setField3("值3");
        System.out.println(product);
    }
}
