package com.blackj.springbootdemo.design.template;

/**
 * <AUTHOR>
 * Description 模版方法设计模式
 */
public class TemplatePattern {
    /**
     * 优惠券计算接口
     */
    interface DiscountCalculate {
        void calculate();
    }

    public static abstract class AbstractDiscountCalculate implements DiscountCalculate {
        @Override
        public void calculate() {
            commonCalculate();
            specialCalculate();
        }

        /**
         * 通用计算公式
         */
        private void commonCalculate() {
            System.out.println("优惠券通用计算公式");
        }

        /**
         * 特别计算公式
         */
        protected abstract void specialCalculate();
    }

    public static class DiscountCalculate1 extends AbstractDiscountCalculate {
        @Override
        protected void specialCalculate() {
            System.out.println("特殊计算公式1");
        }
    }

    public static class DiscountCalculate2 extends AbstractDiscountCalculate {
        @Override
        protected void specialCalculate() {
            System.out.println("特殊计算公式2");
        }
    }

    public static class DiscountCalculate3 extends AbstractDiscountCalculate {
        @Override
        protected void specialCalculate() {
            System.out.println("特殊计算公式3");
        }
    }

    public static void main(String[] args) {
        /*
        如果要修改通用的计算公式，只要改父类AbstractDiscountCalculate的通用方法就可以了。
         */
        DiscountCalculate1 discountCalculate1 = new DiscountCalculate1();
        discountCalculate1.calculate();

        DiscountCalculate2 discountCalculate2 = new DiscountCalculate2();
        discountCalculate2.calculate();

        DiscountCalculate3 discountCalculate3 = new DiscountCalculate3();
        discountCalculate3.calculate();
    }
}
