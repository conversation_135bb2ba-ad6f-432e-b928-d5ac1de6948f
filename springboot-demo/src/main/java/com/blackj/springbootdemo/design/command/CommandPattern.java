package com.blackj.springbootdemo.design.command;

/**
 * <AUTHOR>
 * Description 命令模式
 * 这里用读写两种请求分别封装到不同的命令类中做演示
 */
public class CommandPattern {
    /**
     * 命令接口
     */
    interface Command {
        void execute();
    }

    public static class ReadCommand implements Command {
        @Override
        public void execute() {
            System.out.println("执行读操作");
        }
    }

    public static class WriteCommand implements Command {
        @Override
        public void execute() {
            System.out.println("执行写操作");
        }
    }

    /**
     * 命令执行类
     */
    public static class CommandInvoker {
        private Command command;

        public void setCommand(Command command) {
            this.command = command;
        }

        public void execute() {
            System.out.println("命令执行前额外的操作");
            command.execute();
            System.out.println("命令执行后额外的操作");
        }
    }

    public static void main(String[] args) {
        // 命令执行类
        CommandInvoker commandInvoker = new CommandInvoker();
        // 读请求
        Command readCommand = new ReadCommand();
        // 写请求
        Command writeCommand = new WriteCommand();

        commandInvoker.setCommand(readCommand);
        commandInvoker.execute();

        commandInvoker.setCommand(writeCommand);
        commandInvoker.execute();
    }
}
