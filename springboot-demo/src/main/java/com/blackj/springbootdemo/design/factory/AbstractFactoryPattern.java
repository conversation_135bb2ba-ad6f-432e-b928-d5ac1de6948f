package com.blackj.springbootdemo.design.factory;

/**
 * <AUTHOR>
 * Description 抽象工厂方法
 */
public class AbstractFactoryPattern {
    /**
     * 产品A接口
     */
    public interface ProductA {
        void print();
    }

    public static class ProductA1 implements ProductA {
        @Override
        public void print() {
            System.out.println("产品A1");
        }
    }

    public static class ProductA2 implements ProductA {
        @Override
        public void print() {
            System.out.println("产品A2");
        }
    }

    /**
     * 产品B接口
     */
    public interface ProductB {
        void print();
    }

    public static class ProductB1 implements ProductB {
        @Override
        public void print() {
            System.out.println("产品B1");
        }
    }

    public static class ProductB2 implements ProductB {
        @Override
        public void print() {
            System.out.println("产品B2");
        }
    }

    public interface Factory {
        ProductA createProductA();
        ProductB createProductB();
    }

    /**
     * 产品A1 + 产品B1 组合
     */
    public static class Factory1 implements Factory {
        private final static Factory1 INSTANCE = new Factory1();

        @Override
        public ProductA createProductA() {
            return new ProductA1();
        }

        @Override
        public ProductB createProductB() {
            return new ProductB1();
        }

        public static Factory1 getInstance() {
            return INSTANCE;
        }
    }

    /**
     * 产品A2 + 产品B2 组合
     */
    public static class Factory2 implements Factory {
        private final static Factory2 INSTANCE = new Factory2();

        @Override
        public ProductA createProductA() {
            return new ProductA2();
        }

        @Override
        public ProductB createProductB() {
            return new ProductB2();
        }

        public static Factory2 getInstance() {
            return INSTANCE;
        }
    }

    public static void main(String[] args) {
        // 产品A1+B1组合
        ProductA productA1 = Factory1.getInstance().createProductA();
        ProductB productB1 = Factory1.getInstance().createProductB();
        productA1.print();
        productB1.print();

        // 产品A2+B2组合
        ProductA productA2 = Factory2.getInstance().createProductA();
        ProductB productB2 = Factory2.getInstance().createProductB();
        productA2.print();
        productB2.print();
    }
}
