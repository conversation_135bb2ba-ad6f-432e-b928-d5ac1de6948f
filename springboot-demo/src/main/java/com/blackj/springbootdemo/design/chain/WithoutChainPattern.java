package com.blackj.springbootdemo.design.chain;

/**
 * <AUTHOR>
 * Description 不使用责任链模式
 */
public class WithoutChainPattern {
    public static void main(String[] args) {
        // 业务流程A，执行顺序：1 -> 2 -> 3
        System.out.println("执行功能1");
        System.out.println("执行功能2");
        System.out.println("执行功能3");

        // 业务流程B，执行顺序：2 -> 1 -> 3
        System.out.println("执行功能2");
        System.out.println("执行功能1");
        System.out.println("执行功能3");
    }
}
