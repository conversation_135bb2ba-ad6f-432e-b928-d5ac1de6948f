package com.blackj.springbootdemo.design.visitor;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Description 访问者模式
 * 一般都是配合组合模式一起使用，可以随时扩展功能
 */
public class VisitorPattern {
    /**
     * 部门类
     */
    public static class Department {
        private String name;
        private List<Department> children = new ArrayList<>();

        public Department(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public List<Department> getChildren() {
            return children;
        }

        public void accept(Visitor visitor) {
            visitor.visit(this);
        }
    }

    /**
     * 访问者接口
     */
    interface Visitor {
        void visit(Department department);
    }

    public static class PrintVisitor implements Visitor {
        @Override
        public void visit(Department department) {
            System.out.println("部门【" + department.getName() + "】");
            if (department.getChildren().size() > 0) {
                for (Department dept : department.getChildren()) {
                    dept.accept(this);
                }
            }
        }
    }

    public static class RemoveVisitor implements Visitor {
        @Override
        public void visit(Department department) {
            if (department.getChildren().size() > 0) {
                for (Department dept : department.getChildren()) {
                    dept.accept(this);
                }
            }
            System.out.println("删除部门【" + department.getName() + "】");
        }
    }

    public static void main(String[] args) {
        // 创建部门
        Department leafDeptA = new Department("叶子部门A");
        Department leafDeptB = new Department("叶子部门B");
        Department leafDeptC = new Department("叶子部门C");

        Department subDeptA = new Department("子部门A");
        Department subDeptB = new Department("子部门B");

        Department parentDept = new Department("父部门");

        // 级联部门
        subDeptA.getChildren().add(leafDeptA);
        subDeptA.getChildren().add(leafDeptB);
        subDeptB.getChildren().add(leafDeptC);
        parentDept.getChildren().add(subDeptA);
        parentDept.getChildren().add(subDeptB);

        // 级联打印
        Visitor printVisitor = new PrintVisitor();
        parentDept.accept(printVisitor);

        // 级联删除
        Visitor removeVisitor = new RemoveVisitor();
        parentDept.accept(removeVisitor);
    }
}
