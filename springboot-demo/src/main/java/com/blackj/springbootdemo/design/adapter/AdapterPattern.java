package com.blackj.springbootdemo.design.adapter;

/**
 * <AUTHOR>
 * Description 适配器模式
 */
public class AdapterPattern {
    // 老接口
    public interface OldInterface {
        void oldExecute();
    }

    // 老接口实现类
    public static class OldInterfaceImpl implements OldInterface {
        @Override
        public void oldExecute() {
            System.out.println("执行老接口逻辑");
        }
    }

    // 新接口
    public interface NewInterface {
        void newExecute();
    }

    // 新接口实现类
    public static class NewInterfaceImpl implements NewInterface {
        @Override
        public void newExecute() {
            System.out.println("执行新接口逻辑");
        }
    }

    // 定义一个适配器类
    public static class NewInterfaceAdapter implements NewInterface {
        // 老接口
        private OldInterface oldInterface;

        public NewInterfaceAdapter(OldInterface oldInterface) {
            this.oldInterface = oldInterface;
        }

        @Override
        public void newExecute() {
            oldInterface.oldExecute();
        }
    }

    public static void main(String[] args) {
        NewInterface oldInterface = new NewInterfaceAdapter(new OldInterfaceImpl());
        NewInterface newInterface = new NewInterfaceImpl();

        oldInterface.newExecute();
        newInterface.newExecute();
    }
}
