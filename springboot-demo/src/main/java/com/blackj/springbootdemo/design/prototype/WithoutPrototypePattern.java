package com.blackj.springbootdemo.design.prototype;

/**
 * <AUTHOR>
 * Description 拷贝对象，不使用原型模式
 */
public class WithoutPrototypePattern {
    public static class Component {
        private String name;

        public Component(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        @Override
        public String toString() {
            return "Component{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    public static class Product {
        private String name;
        private Component component;

        public Product(String name, Component component) {
            this.name = name;
            this.component = component;
        }

        public String getName() {
            return name;
        }

        public Component getComponent() {
            return component;
        }

        @Override
        public String toString() {
            return "Product{" +
                    "name='" + name + '\'' +
                    ", component=" + component +
                    '}';
        }
    }

    public static void main(String[] args) {
        // 需要拷贝一个对象，这里只能手动进行拷贝
        Product product = new Product("测试产品", new Component("组件"));
        Product copyProduct = new Product(product.getName(), new Component(product.getComponent().getName()));

        System.out.println(product);
        System.out.println(copyProduct);
    }
}
