package com.blackj.springbootdemo.design.iterator;

/**
 * <AUTHOR>
 * Description 普通遍历实现
 */
public class WithoutIteratorPattern {
    public static class Student {
        private String name;

        public Student(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "Student{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    public static class Classroom {
        private Student[] students;

        public Student[] getStudents() {
            return students;
        }

        public void setStudents(Student[] students) {
            this.students = students;
        }
    }

    public static void main(String[] args) {
        Student[] students = new Student[2];
        students[0] = new Student("张三");
        students[1] = new Student("李四");
        Classroom classroom = new Classroom();
        classroom.setStudents(students);

        // 遍历
        for (Student student : classroom.getStudents()) {
            System.out.println(student);
        }
    }
}
