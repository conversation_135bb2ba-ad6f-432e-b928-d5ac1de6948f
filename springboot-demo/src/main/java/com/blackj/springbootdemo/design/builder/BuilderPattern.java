package com.blackj.springbootdemo.design.builder;

/**
 * <AUTHOR>
 * Description 使用构造器模式，将复杂的对象构造逻辑进行封装
 */
public class BuilderPattern {
    public static class Product {
        private String field1;
        private String field2;
        private String field3;

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public String getField2() {
            return field2;
        }

        public void setField2(String field2) {
            this.field2 = field2;
        }

        public String getField3() {
            return field3;
        }

        public void setField3(String field3) {
            this.field3 = field3;
        }

        @Override
        public String toString() {
            return "Product{" +
                    "field1='" + field1 + '\'' +
                    ", field2='" + field2 + '\'' +
                    ", field3='" + field3 + '\'' +
                    '}';
        }
    }

    /**
     * Builder 接口
     */
    interface Builder {
        Builder field1(String value);
        Builder field2(String value);
        Builder field3(String value);
        Product create();
    }

    /**
     * Builder 接口实现类
     */
    public static class ProductBuilder implements Builder {
        private Product product = new Product();

        @Override
        public Builder field1(String value) {
            System.out.println("对 field1 属性进行复杂校验");
            product.setField1(value);
            return this;
        }

        @Override
        public Builder field2(String value) {
            System.out.println("对 field2 属性进行格式化");
            product.setField2(value);
            return this;
        }

        @Override
        public Builder field3(String value) {
            System.out.println("对 field3 属性进行复杂逻辑处理");
            product.setField3(value);
            return this;
        }

        @Override
        public Product create() {
            return product;
        }
    }

    public static void main(String[] args) {
        // 构建一个产品
        Product product = new ProductBuilder()
                .field1("值1")
                .field2("值2")
                .field3("值3")
                .create();
        System.out.println(product);
    }
}
