package com.blackj.springbootdemo.design.adapter;

/**
 * <AUTHOR>
 * Description 不使用适配器模式
 */
public class WithoutAdapterPattern {
    // 老接口
    public interface OldInterface {
        void oldExecute();
    }

    // 老接口实现类
    public static class OldInterfaceImpl implements OldInterface {
        @Override
        public void oldExecute() {
            System.out.println("执行老接口逻辑");
        }
    }

    // 新接口
    public interface NewInterface {
        void newExecute();
    }

    // 新接口实现类
    public static class NewInterfaceImpl implements NewInterface {
        @Override
        public void newExecute() {
            System.out.println("执行新接口逻辑");
        }
    }

    public static void main(String[] args) {
        OldInterface oldInterface = new OldInterfaceImpl();
        NewInterface newInterface = new NewInterfaceImpl();

        oldInterface.oldExecute();
        newInterface.newExecute();
    }
}
