package com.blackj.springbootdemo.design.observer;

import java.util.Observable;
import java.util.Observer;

/**
 * <AUTHOR>
 * Description 观察者模式
 * 这里使用 JDK 自带的框架演示
 */
public class ObserverPattern {
    public static class Subject extends Observable {
        private Integer state;

        public Subject(Integer state) {
            this.state = state;
        }

        public void setState(Integer state) {
            this.state = state;
            // 值变了后通知观察者
            this.setChanged();
            this.notifyObservers(state);
        }
    }

    public static class ConcreteObserver implements Observer {
        @Override
        public void update(Observable o, Object arg) {
            Integer state = (Integer) arg;
            System.out.println("state:" + state);
        }
    }

    public static void main(String[] args) {
        Subject subject = new Subject(0);
        // 创建观察者
        ConcreteObserver concreteObserver = new ConcreteObserver();
        subject.addObserver(concreteObserver);
        // 改变state值
        subject.setState(1);
        subject.setState(2);
    }
}
