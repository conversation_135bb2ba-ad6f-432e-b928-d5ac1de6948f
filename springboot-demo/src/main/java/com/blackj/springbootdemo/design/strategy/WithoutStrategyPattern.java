package com.blackj.springbootdemo.design.strategy;

/**
 * <AUTHOR>
 * Description 不使用策略模式
 */
public class WithoutStrategyPattern {
    public static void main(String[] args) {
        // 根据参数不同，执行不同的优惠计价方式
        int discountStyle = 1;

        if (discountStyle == 1) {
            System.out.println("执行第一种优惠计价方式");
        } else if (discountStyle == 2) {
            System.out.println("执行第二种优惠计价方式");
        } else {
            System.out.println("执行默认的优惠计价方式");
        }
    }
}
