package com.blackj.springbootdemo.design.state;

/**
 * <AUTHOR>
 * Description 状态模式
 * 用于状态间的流转，比如销售单子：新建、待审批、已审批、已完成
 */
public class StatePattern {
    interface State {
        void execute();
    }

    public static class NewState implements State {
        @Override
        public void execute() {
            System.out.println("执行新建销售单的功能逻辑");
        }
    }

    public static class ApprovingState implements State {
        @Override
        public void execute() {
            System.out.println("执行销售单待审批的功能逻辑");
        }
    }

    public static class ApprovedState implements State {
        @Override
        public void execute() {
            System.out.println("执行销售单已审批的功能逻辑");
        }
    }

    public static class FinishedState implements State {
        @Override
        public void execute() {
            System.out.println("执行销售单已完成的功能逻辑");
        }
    }

    public static class Context {
        private State state;

        public Context(State state) {
            this.state = state;
            this.state.execute();
        }

        public void execute(int stateType) {
            if (stateType == 1) {
                this.state = new ApprovingState();
                this.state.execute();
            } else if (stateType == 2) {
                this.state = new ApprovedState();
                this.state.execute();
            } else if (stateType == 3) {
                this.state = new FinishedState();
                this.state.execute();
            }
        }
    }

    public static void main(String[] args) {
        Context context = new Context(new NewState());
        context.execute(1);
        context.execute(2);
        context.execute(3);
    }
}
