package com.blackj.springbootdemo.design.chain;

/**
 * <AUTHOR>
 * Description 责任链模式
 */
public class ChainPattern {
    public static abstract class Handler {
        // 下一个执行节点
        protected Handler successor;

        public Handler(Handler successor) {
            this.successor = successor;
        }

        public abstract void execute();
    }

    public static class Handler1 extends Handler {
        public Handler1(Handler successor) {
            super(successor);
        }

        @Override
        public void execute() {
            System.out.println("执行功能1");
            if (successor != null) {
                // 执行下一个节点
                successor.execute();
            }
        }
    }

    public static class Handler2 extends Handler {
        public Handler2(Handler successor) {
            super(successor);
        }

        @Override
        public void execute() {
            System.out.println("执行功能2");
            if (successor != null) {
                // 执行下一个节点
                successor.execute();
            }
        }
    }

    public static class Handler3 extends Handler {
        public Handler3(Handler successor) {
            super(successor);
        }

        @Override
        public void execute() {
            System.out.println("执行功能3");
            if (successor != null) {
                // 执行下一个节点
                successor.execute();
            }
        }
    }

    public static void main(String[] args) {
        // 业务流程A，执行顺序：1 -> 2 -> 3
        Handler thirdHandler = new Handler3(null);
        Handler secondHandler = new Handler2(thirdHandler);
        Handler firstHandler = new Handler1(secondHandler);
        firstHandler.execute();
        System.out.println("--------------");

        // 业务流程B，执行顺序：2 -> 1 -> 3
        thirdHandler = new Handler3(null);
        secondHandler = new Handler1(thirdHandler);
        firstHandler = new Handler2(secondHandler);
        firstHandler.execute();
    }
}
