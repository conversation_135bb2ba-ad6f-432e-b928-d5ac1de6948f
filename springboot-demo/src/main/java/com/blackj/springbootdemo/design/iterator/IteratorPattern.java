package com.blackj.springbootdemo.design.iterator;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * Description 使用迭代器模式
 */
public class IteratorPattern {
    // 声明一个迭代器接口
    public interface Iterator<T> {
        boolean hasNext();
        T next();
    }

    // 声明一个集合接口
    public interface Aggregate<T> {
        Iterator<T> iterator();
    }

    // 学生类
    public static class Student {
        private String name;

        public Student(String name) {
            this.name = name;
        }

        @Override
        public String toString() {
            return "Student{" +
                    "name='" + name + '\'' +
                    '}';
        }
    }

    // 教室类
    public static class Classroom implements Aggregate<Student> {
        private final List<Student> students;

        public Classroom(int size) {
            this.students = new ArrayList<>(size);
        }

        public Student getStudent(int index) {
            return students.get(index);
        }

        public void addStudent(Student student) {
            this.students.add(student);
        }

        public int getLength() {
            return this.students.size();
        }

        @Override
        public Iterator<Student> iterator() {
            return new ClassroomIterator(this);
        }
    }

    // 教室迭代器
    public static class ClassroomIterator implements Iterator<Student> {
        private Classroom classroom;
        private int index;

        public ClassroomIterator(Classroom classroom) {
            this.classroom = classroom;
            this.index = 0;
        }

        @Override
        public boolean hasNext() {
            return index < classroom.getLength();
        }

        @Override
        public Student next() {
            return classroom.getStudent(index++);
        }
    }

    public static void main(String[] args) {
        Classroom classroom = new Classroom(2);
        classroom.addStudent(new Student("张三"));
        classroom.addStudent(new Student("李四"));

        // 遍历
        Iterator<Student> iterator = classroom.iterator();
        while (iterator.hasNext()) {
            System.out.println(iterator.next());
        }
    }
}
