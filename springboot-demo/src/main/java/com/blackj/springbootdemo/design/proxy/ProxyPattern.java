package com.blackj.springbootdemo.design.proxy;

/**
 * <AUTHOR>
 * Description 代理模式
 */
public class ProxyPattern {
    interface Subject {
        void request();
    }

    public static class ConcreteSubject implements Subject {
        @Override
        public void request() {
            System.out.println("开始执行请求");
        }
    }

    public static class Proxy implements Subject {
        private final Subject subject;

        public Proxy(Subject subject) {
            this.subject = subject;
        }

        @Override
        public void request() {
            // 在请求执行前，做一系列其他的操作
            System.out.println("执行请求前的额外操作");
            subject.request();
        }
    }

    public static void main(String[] args) {
        Subject subject = new ConcreteSubject();
        // 创建代理类
        Proxy proxy = new Proxy(subject);
        proxy.request();
    }
}
