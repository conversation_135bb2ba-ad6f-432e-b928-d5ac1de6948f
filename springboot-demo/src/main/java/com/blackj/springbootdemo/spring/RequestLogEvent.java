package com.blackj.springbootdemo.spring;

import org.aspectj.lang.ProceedingJoinPoint;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * Description
 */
public class RequestLogEvent extends ApplicationEvent {
    private Object result;

    public RequestLogEvent(ProceedingJoinPoint source, Object result) {
        super(source);
        this.result = result;
    }

    public Object getResult() {
        return result;
    }
}
