package com.blackj.springbootdemo.web;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * Description
 */
@RestController
@RequestMapping("/test/file")
public class FileTestController {
    @PostMapping("/upload")
    public Map<String, String> testUpload(MultipartFile multipartFile) {
        Map<String, String> result = new HashMap<>(4);
        result.put("name", multipartFile.getName());
        result.put("originFileName", multipartFile.getOriginalFilename());
        result.put("contentType", multipartFile.getContentType());
        return result;
    }
}
