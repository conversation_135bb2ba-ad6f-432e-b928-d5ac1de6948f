package com.blackj.springbootdemo.utils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

public class TimezoneUtils {

    public static long getTimestampFromTimeString(String timezone, String timeString) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone(timezone));
        Date date = sdf.parse(timeString);
        return date.getTime();
    }

    public static String getTimeStringFromTimestamp(String timezone, long timestamp) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone(timezone));
        return sdf.format(new Date(timestamp));
    }
}

