package com.blackj.springbootdemo.utils;

import com.blackj.springbootdemo.consts.DateFormatEnum;
import com.blackj.springbootdemo.consts.DatePatternConst;
import org.apache.commons.lang.StringUtils;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * 涉及时区的日期工具类
 *
 * <AUTHOR>
 */
public class ZoneDateUtil {
    private static DateTimeFormatter getFormatter(String pattern) {
        DateFormatEnum dateFormatEnum = DateFormatEnum.getDateFormatter(pattern);
        if (Objects.isNull(dateFormatEnum)) {
            throw new RuntimeException("Date format pattern is not support.");
        }
        return dateFormatEnum.getFormatter();
    }

    /**
     * 根据时间戳获取时间字符串
     *
     * @param timeZone  时区
     * @param timeStamp 时间戳
     * @param pattern   格式化参数
     * @return 时间字符串
     */
    public static String format(String timeZone, long timeStamp, String pattern) {
        // 特殊处理，美国冬令时时间取洛杉矶时间
        if ("America/Whitehorse".equals(timeZone)) {
            timeZone = "America/Los_Angeles";
        }

        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeStamp), ZoneId.of(timeZone));
        return getFormatter(pattern).format(zonedDateTime);
    }

    /**
     * 根据时间字符串获取时间字符串
     *
     * @param timeZone  时区
     * @param date      时间字符串
     * @param pattern   格式化参数
     * @return 时间字符串
     */
    public static String format(String timeZone, String date, String pattern) {
        // 特殊处理，美国冬令时时间取洛杉矶时间
        if ("America/Whitehorse".equals(timeZone)) {
            timeZone = "America/Los_Angeles";
        }

        long timeStamp = timestamp(timeZone, date, pattern);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(Instant.ofEpochMilli(timeStamp), ZoneId.of(timeZone));
        return getFormatter(pattern).format(zonedDateTime);
    }

    /**
     * 根据时间字符串获取时间戳
     *
     * @param timeZone 时区
     * @param date     时间字符串
     * @param pattern  格式化参数
     * @return 时间戳
     */
    public static Long timestamp(String timeZone, String date, String pattern) {
        // 特殊处理，美国冬令时时间取洛杉矶时间
        if ("America/Whitehorse".equals(timeZone)) {
            timeZone = "America/Los_Angeles";
        }

        ZonedDateTime zonedDateTime;
        DateTimeFormatter formatter = getFormatter(pattern);
        if (formatter == DateFormatEnum.DATE_PATTERN.getFormatter()) {
            zonedDateTime = ZonedDateTime.of(LocalDate.parse(date, formatter),
                    LocalTime.parse("00:00:00", DateFormatEnum.TIME_PATTERN.getFormatter()), ZoneId.of(timeZone));
        } else {
            zonedDateTime = ZonedDateTime.of(LocalDateTime.parse(date, formatter), ZoneId.of(timeZone));
        }
        return zonedDateTime.toEpochSecond() * 1000;
    }

    /**
     * 获取GMT时间字符串
     *
     * @param datetime 日期
     * @param timeZone 时区
     * @return GMT时间字符串
     */
    public static String toGMTString(LocalDateTime datetime, String timeZone) {
        ZoneId zoneId = ZoneId.of(timeZone);
        DateTimeFormatter formatter = DateTimeFormatter.RFC_1123_DATE_TIME.withZone(zoneId);
        return formatter.format(datetime.atZone(zoneId).toInstant());
    }

    /**
     * 获取国际标准ISO 8601日期格式
     *
     * @param date     日期
     * @param timeZone 时区
     * @return IOS 8601时间格式
     */
    public static String toISO8601Date(String date, String timeZone) {
        LocalDate localDate = LocalDate.parse(date, getFormatter(DatePatternConst.DATE_PATTERN));
        DateTimeFormatter isoFormatter = DateTimeFormatter.ofPattern("yyyy-MM-ddXXX");
        ZonedDateTime zonedDateTime = localDate.atStartOfDay(ZoneId.of(timeZone));

        return zonedDateTime.format(isoFormatter);
    }

    /**
     * 获取国际标准ISO 8601时间格式
     *
     * @param datetime     日期时间
     * @param timeZone 时区
     * @return IOS 8601时间格式
     */
    public static String toISO8601DateTime(String datetime, String timeZone) {
        LocalDateTime localDateTime = LocalDateTime.parse(datetime, getFormatter(DatePatternConst.DATE_TIME_PATTERN));
        DateTimeFormatter isoFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");
        ZonedDateTime zonedDateTime = ZonedDateTime.of(localDateTime, ZoneId.of(timeZone));

        return zonedDateTime.format(isoFormatter);
    }

    /**
     * 获取当天最晚时刻时间戳
     *
     * @param timeZone 时区
     * @return 时间戳
     */
    public static Long endDayTimestamp(String timeZone) {
        String dateStr = format(timeZone, System.currentTimeMillis(), DatePatternConst.DATE_PATTERN);
        return timestamp(timeZone, dateStr + " 23:59:59", DatePatternConst.DATE_TIME_PATTERN);
    }
}
