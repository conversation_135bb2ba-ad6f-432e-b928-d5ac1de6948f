package com.blackj.springbootdemo.utils;

import org.apache.shiro.crypto.hash.Sha256Hash;

import java.util.regex.Pattern;

/**
 * 密码工具类
 *
 * <AUTHOR>
 */
public class PasswordUtil {
    /**
     * 生成盐
     *
     * @param count 位数
     * @return 盐
     */
    public static String generateSalt(int count) {
        return StringUtil.random(count);
    }

    /**
     * 生成加密密码
     *
     * @param password 密码明文
     * @param salt     盐
     * @return Encrypt Password
     */
    public static String encryptPassword(CharSequence password, String salt) {
        return new Sha256Hash(password, salt).toHex();
    }

    /**
     * 密码匹配
     *
     * @param originPassword  密码明文
     * @param encryptPassword 加密密码
     * @param salt            盐
     * @return true/false
     */
    public static boolean match(CharSequence originPassword, String encryptPassword, String salt) {
        return new Sha256Hash(originPassword, salt).toHex().equals(encryptPassword);
    }

    /**
     * 校验密码规则
     *
     * @param password 密码
     * @return true/false
     */
    public static boolean checkRules(String password) {
        String pattern = "^(?=.*[0-9])(?=.*[a-zA-Z])[0-9A-Za-z~!@#$%^&*():,;'=?./-]{8,30}$";
        return Pattern.matches(pattern, password);
    }
}
