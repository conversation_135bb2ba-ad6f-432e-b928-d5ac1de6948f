package com.blackj.springbootdemo.utils;

import net.sf.cglib.beans.BeanCopier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description Bean Copy 工具类
 */
public class BeanCopierUtil {
    private static Logger log = LoggerFactory.getLogger(BeanCopierUtil.class);
    private static Map<String, BeanCopier> beanCopierCacheMap = new HashMap<>();

    public static void copyProperties(Object source, Object target) {
        String cacheKey = getCacheKey(source, target);
        BeanCopier beanCopier = null;

        if (!beanCopierCacheMap.containsKey(cacheKey)) {
            synchronized (BeanCopierUtil.class) {
                if (!beanCopierCacheMap.containsKey(cacheKey)) {
                    beanCopier = BeanCopier.create(source.getClass(), target.getClass(), false);
                    beanCopierCacheMap.put(cacheKey, beanCopier);
                }
            }
        } else {
            beanCopier = beanCopierCacheMap.get(cacheKey);
        }

        beanCopier.copy(source, target, null);
    }

    public static <T> T copyProperties(Object source, Class<T> targetClass) {
        try {
            T target = targetClass.newInstance();
            copyProperties(source, target);
            return target;
        } catch (Exception e) {
            log.error("复制类{}属性时发生错误", targetClass.getCanonicalName(), e);
        }
        return null;
    }

    private static String getCacheKey(Object source, Object target) {
        return source.getClass().toString() + target.getClass().toString();
    }
}
