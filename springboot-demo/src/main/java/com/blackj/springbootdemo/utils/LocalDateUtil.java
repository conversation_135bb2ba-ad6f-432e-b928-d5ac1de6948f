package com.blackj.springbootdemo.utils;

import com.blackj.springbootdemo.consts.DateFormatEnum;
import com.blackj.springbootdemo.consts.DatePatternConst;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 本地日期工具类
 *
 * <AUTHOR>
 */
public class LocalDateUtil {
    private static DateTimeFormatter getFormatter(String pattern) {
        DateFormatEnum dateFormatEnum = DateFormatEnum.getDateFormatter(pattern);
        if (Objects.isNull(dateFormatEnum)) {
            throw new RuntimeException("Date format pattern is not support.");
        }
        return dateFormatEnum.getFormatter();
    }

    /**
     * 日期格式化
     *
     * @param date    日期
     * @param pattern 格式
     * @return 格式化后的日期
     */
    public static String format(LocalDate date, String pattern) {
        DateTimeFormatter formatter = getFormatter(pattern);
        return date.format(formatter);
    }

    /**
     * 日期格式转化，从旧格式转化为新格式
     *
     * @param date          日期字符串
     * @param originPattern 旧格式
     * @param targetPattern 新格式
     * @return 新格式日期字符串
     */
    public static String format(String date, String originPattern, String targetPattern) {
        DateTimeFormatter originFormatter = getFormatter(originPattern);
        DateTimeFormatter targetFormatter = getFormatter(targetPattern);

        if (date.length() == 10) {
            LocalDate parseDate = LocalDate.parse(date, originFormatter);
            return parseDate.format(targetFormatter);
        }

        LocalDateTime parseDateTime = LocalDateTime.parse(date, originFormatter);
        return parseDateTime.format(targetFormatter);
    }

    /**
     * 日期加减
     *
     * @param date 日期字符串
     * @param days 天数
     * @return 新日期
     */
    public static String plusDay(String date, int days, String pattern) {
        DateFormatEnum dateFormatEnum = DateFormatEnum.getDateFormatter(pattern);
        if (dateFormatEnum == null) {
            throw new RuntimeException("Date format pattern is not support.");
        }
        DateTimeFormatter formatter = dateFormatEnum.getFormatter();

        switch (dateFormatEnum) {
            case DATE_PATTERN:
                LocalDate parseDate = LocalDate.parse(date, formatter).plusDays(days);
                return parseDate.format(formatter);
            case DATE_TIME_PATTERN:
                LocalDateTime parseDateTime = LocalDateTime.parse(date, formatter).plusDays(days);
                return parseDateTime.format(formatter);
            default:
                throw new RuntimeException("Date format pattern is not support.");
        }
    }

    /**
     * 获取相差的天数
     *
     * @param minuend  被减日期
     * @param subtract 减日期
     * @return 天数
     */
    public static int minusToDay(String minuend, String subtract) {
        if (minuend.length() > DatePatternConst.DATE_PATTERN.length()) {
            minuend = minuend.substring(0, 10);
        }
        if (subtract.length() > DatePatternConst.DATE_PATTERN.length()) {
            subtract = subtract.substring(0, 10);
        }

        DateTimeFormatter formatter = getFormatter(DatePatternConst.DATE_PATTERN);
        LocalDate minuendDate = LocalDate.parse(minuend, formatter);
        LocalDate subtractDate = LocalDate.parse(subtract, formatter);
        return (int) (minuendDate.toEpochDay() - subtractDate.toEpochDay());
    }

    /**
     * 时间日期字符串日期字符串
     */
    public static String dateTime2Date(String dateTimeStr) {
        if (dateTimeStr.length() == 19) {
            return dateTimeStr.substring(0, 10);
        }
        return dateTimeStr;
    }

    /**
     * 获取当前年份
     *
     * @return 年份，e.g. 1970
     */
    public static int getCurrentYear() {
        return LocalDate.now().getYear();
    }

    /**
     * 获取时间戳
     *
     * @param date 时间
     * @return 时间戳
     */
    public static long getTimestamp(String date) {
        if (date.length() == 10) {
            date = date + " 00:00:00";
        }
        DateTimeFormatter formatter = getFormatter(DatePatternConst.DATE_TIME_PATTERN);
        LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);
        return localDateTime.atZone(ZoneOffset.systemDefault()).toInstant().toEpochMilli();
    }

    /**
     * 返回两个时间间隔内的所有日期,含首不含尾
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 日期列表
     */
    public static List<String> getBetweenDate(String startDate, String endDate) {
        List<String> result = new ArrayList<>();
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        while (start.isBefore(end)) {
            result.add(start.toString());
            start = start.plusDays(1);
        }
        return result;
    }

    /**
     * 判断日期是否在指定日期之间，包含首尾
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param date      日期
     * @return true/false
     */
    public static boolean existBetweenDate(String startDate, String endDate, String date) {
        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        LocalDate checkDate = LocalDate.parse(date);

        return !checkDate.isBefore(start) && !checkDate.isAfter(end);
    }

    /**
     * 判断日期是否在指定日期之间，包含首尾
     *
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @param date      日期
     * @return true/false
     */
    public static boolean existBetweenDateTime(String startDate, String endDate, String date) {
        LocalDateTime start = LocalDateTime.parse(startDate);
        LocalDateTime end = LocalDateTime.parse(endDate);
        LocalDateTime checkDate = LocalDateTime.parse(date);

        return !checkDate.isBefore(start) && !checkDate.isAfter(end);
    }

    /**
     * 判断日期是否在指定日期之前
     *
     * @param date        日期
     * @param compareDate 比较日期
     * @param pattern     日期格式
     * @return true/false
     */
    public static boolean after(String date, String compareDate, String pattern) {
        DateFormatEnum dateFormatEnum = DateFormatEnum.getDateFormatter(pattern);
        if (dateFormatEnum == null) {
            throw new RuntimeException("Date format pattern is not support.");
        }
        DateTimeFormatter formatter = dateFormatEnum.getFormatter();

        switch (dateFormatEnum) {
            case DATE_PATTERN:
                LocalDate localDate = LocalDate.parse(date, formatter);
                LocalDate compareLocalDate = LocalDate.parse(compareDate, formatter);
                return localDate.isAfter(compareLocalDate);
            case DATE_TIME_PATTERN:
                LocalDateTime localDateTime = LocalDateTime.parse(date, formatter);
                LocalDateTime compareLocalDateTime = LocalDateTime.parse(compareDate, formatter);
                return localDateTime.isAfter(compareLocalDateTime);
            default:
                throw new RuntimeException("Date format pattern is not support.");
        }
    }
}