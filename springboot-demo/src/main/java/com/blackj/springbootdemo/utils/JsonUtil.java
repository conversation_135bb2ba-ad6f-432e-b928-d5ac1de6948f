package com.blackj.springbootdemo.utils;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.List;

/**
 * jackjson 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class JsonUtil {
    private static final ObjectMapper MAPPER = new ObjectMapper();

    static {
        MAPPER.configure(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY, true);
        MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        // 日期格式
        MAPPER.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

        // Include.NON_NULL 属性为NULL 不序列化
        // ALWAYS 默认策略，任何情况都执行序列化
        // NON_EMPTY null、集合数组等没有内容、空字符串等，都不会被序列化
        // NON_DEFAULT 如果字段是默认值，就不会被序列化
        // NON_ABSENT null的不会序列化，但如果类型是AtomicReference，依然会被序列化
        MAPPER.setSerializationInclusion(JsonInclude.Include.ALWAYS);

        // 允许字段名没有引号（可以进一步减小json体积）：
        MAPPER.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);

        // 允许单引号：
        MAPPER.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);

        // 允许出现特殊字符和转义符
        MAPPER.configure(JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS.mappedFeature(), true);

        // 允许C和C++样式注释：
        MAPPER.configure(JsonParser.Feature.ALLOW_COMMENTS, true);

        // 枚举输出成字符串
        // WRITE_ENUMS_USING_INDEX：输出索引
        MAPPER.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);

        // 空对象不要抛出异常：
        MAPPER.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

        // Date、Calendar等序列化为时间格式的字符串(如果不执行以下设置，就会序列化成时间戳格式)：
        MAPPER.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 反序列化时，遇到未知属性不要抛出异常：
        MAPPER.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        // 反序列化时，遇到忽略属性不要抛出异常：
        MAPPER.disable(DeserializationFeature.FAIL_ON_IGNORED_PROPERTIES);

        // 反序列化时，空字符串对于的实例属性为null：
        MAPPER.enable(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);

        MAPPER.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
    }

    private JsonUtil() {}

    /**
     * 对象转字符串
     */
    public static String toJson(Object obj) {
        if (obj instanceof String) {
            return (String) obj;
        }

        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON transformation error");
        }
    }

    /**
     * 字符串转对象
     */
    public static <T> T toObject(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json) || clazz == null) {
            throw new RuntimeException("json or clazz is null, unable to convert");
        }

        try {
            return MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 字符串转列表
     */
    public static <T> List<T> toArray(String json, Class<T> clazz) {
        if (StringUtils.isEmpty(json) || clazz == null) {
            throw new RuntimeException("json or clazz is null, unable to convert");
        }

        try {
            JavaType javaType = MAPPER.getTypeFactory().constructParametricType(List.class, clazz);
            return MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            throw new RuntimeException("json string cannot be converted to array");
        }
    }
}