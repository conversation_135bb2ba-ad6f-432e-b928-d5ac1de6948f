package com.blackj.springbootdemo.utils;

import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.util.UUID;

public class Base62Util {
    private static final char[] BASE62 = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();
    private static final int BASE = 62;

    /** 将byte[]转为Base62字符串（无符号大整数） */
    public static String encode(byte[] bytes) {
        BigInteger bi = new BigInteger(1, bytes); // 无符号
        StringBuilder sb = new StringBuilder();
        while (bi.compareTo(BigInteger.ZERO) > 0) {
            int mod = bi.mod(BigInteger.valueOf(BASE)).intValue();
            sb.append(BASE62[mod]);
            bi = bi.divide(BigInteger.valueOf(BASE));
        }
        // Pad 0 if necessary (for length compatibility)
        while (sb.length() < 22) { // base62编码16字节（128位）最大22位
            sb.append(BASE62[0]);
        }
        return sb.reverse().toString();
    }

    /** 将Base62字符串转为byte[] */
    public static byte[] decode(String base62) {
        BigInteger bi = BigInteger.ZERO;
        for (char c : base62.toCharArray()) {
            int idx = charToIndex(c);
            if (idx < 0) throw new IllegalArgumentException("Illegal character: " + c);
            bi = bi.multiply(BigInteger.valueOf(BASE)).add(BigInteger.valueOf(idx));
        }
        byte[] bytes = bi.toByteArray();
        // must be 16 bytes for UUID
        // BigInteger.toByteArray() may include a sign byte at the start
        if (bytes.length == 17 && bytes[0] == 0) {
            // remove leading sign byte
            byte[] cut = new byte[16];
            System.arraycopy(bytes, 1, cut, 0, 16);
            return cut;
        } else if (bytes.length < 16) {
            // pad zeros
            byte[] pad = new byte[16];
            System.arraycopy(bytes, 0, pad, 16 - bytes.length, bytes.length);
            return pad;
        } else if (bytes.length > 16) {
            // should not happen for uuid
            throw new IllegalArgumentException("Too many bytes decoded for UUID: " + bytes.length);
        }
        return bytes;
    }

    // 将UUID转为base62字符串
    public static String uuidToBase62(UUID uuid) {
        ByteBuffer bb = ByteBuffer.allocate(16);
        bb.putLong(uuid.getMostSignificantBits());
        bb.putLong(uuid.getLeastSignificantBits());
        return encode(bb.array());
    }

    // 将base62字符串还原为UUID
    public static UUID base62ToUUID(String base62) {
        byte[] bytes = decode(base62);
        ByteBuffer bb = ByteBuffer.wrap(bytes);
        long most = bb.getLong();
        long least = bb.getLong();
        return new UUID(most, least);
    }

    // 生成base62格式UUID
    public static String randomBase62UUID() {
        UUID uuid = UUID.randomUUID();
        return uuidToBase62(uuid);
    }

    private static int charToIndex(char c) {
        if ('0' <= c && c <= '9') return c - '0';
        if ('A' <= c && c <= 'Z') return c - 'A' + 10;
        if ('a' <= c && c <= 'z') return c - 'a' + 36;
        return -1;
    }

    // 示例用法
    public static void main(String[] args) {
        // 生成base62格式UUID
        String b62 = randomBase62UUID();
        System.out.println("base62 uuid: " + b62); // 长度22位

        // 解码回UUID
        UUID uuid = base62ToUUID(b62);
        System.out.println("decoded UUID: " + uuid);

        // 校验
        String b62_2 = uuidToBase62(uuid);
        System.out.println("Equal after encode/decode: " + b62.equals(b62_2));
    }
}