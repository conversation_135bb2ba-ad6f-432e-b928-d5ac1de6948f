# application
spring.application.name=demo-project
server.port=8080

## redis
#spring.redis.host=************
#spring.redis.port=7666
#spring.redis.password=aaBlackJ_Redis_ZjYC_1989bb_Pwdoo
#
## mysql
#mysql.datasource.url=************************************************************************************************************************************************************
#mysql.datasource.username=blackj
#mysql.datasource.password=BlackJ_Grant_Power_1989
#mysql.datasource.driverClassName=com.mysql.jdbc.Driver
#mysql.datasource.initialSize=5
#mysql.datasource.minIdle=5
#mysql.datasource.maxActive=10
#mysql.datasource.maxWait=2000
#mysql.datasource.validationQuery=SELECT 1 FROM DUAL
#mysql.datasource.testOnBorrow=false
#mysql.datasource.testOnReturn=false
#mysql.datasource.testWhileIdle=true
#mysql.datasource.poolPreparedStatements=true
#mysql.datasource.maxPoolPreparedStatementPerConnectionSize=20
#mysql.datasource.timeBetweenEvictionRunsMillis=600000
#mysql.datasource.minEvictableIdleTimeMillis=300000
#mysql.datasource.removeAbandoned=true
#mysql.datasource.removeAbandonedTimeout=180000

# mybatis
mybatis.mapper-locations=classpath:mapper/**/*.xml
mybatis.type-aliases-package=com.blackj.springbootdemo.mapper

# actuator
management.endpoints.web.exposure.include=*
management.endpoints.web.base-path=/actuator